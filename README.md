# Corn Patch Semi-Supervised Learning (FixMatch + EMA)

本项目在 `output/` 目录准备好 patch 图像与标签后，提供一套基于 PyTorch + ViT 的 FixMatch 半监督训练脚手架，并兼容 Windows DirectML（无 NVIDIA CUDA 的设备也可运行）。

## 目录结构

- scripts/
  - dataset_stats.py：统计已标注比例与类别分布
- output/
  - images/\*.png
  - labels/\*.txt（空文件即无标签）
- training/
  - data/tools/make_splits.py：从 output 生成分层划分 CSV
  - datasets/{corn_patches.py, transforms_fixmatch.py}
  - models/build_vit.py
  - utils/{ema.py, logger.py, metrics.py}
  - train_fixmatch.py（训练）
  - eval.py（评估）

## 环境依赖

- Python 3.9+（已在 3.13 上测试通过）
- PyTorch、torchvision、timm、numpy、pillow、scikit-learn、tensorboard、matplotlib
- DirectML（可选，仅 Windows 上的非 CUDA 设备）：torch-directml

安装示例（任选其一执行，不使用 &&）：

- pip install torch torchvision timm numpy pillow scikit-learn tensorboard matplotlib
- 如需 DirectML：pip install torch-directml

## 数据准备

1. 将原始 640×640 PNG 放入 `crop/`，Labelme JSON 放入 `result/`。
2. 运行切块与打标脚本（默认 160、stride 120、阈值 0.6、覆盖率 0.2）：
   - python scripts/make_patches.py
3. 清理删除与重命名（如有手动删图、需要序号连续）：
   - 预览：python scripts/cleanup_and_renumber.py
   - 执行：python scripts/cleanup_and_renumber.py --apply
4. 可视化统计：
   - python scripts/dataset_stats.py

## 数据划分（分层）

- 生成 CSV（默认从 output 读取，写入 training/data/splits）：
  - python training/data/tools/make_splits.py
- 生成的文件：
  - labeled_train.csv / labeled_val.csv / labeled_test.csv / unlabeled.csv
- 说明：
  - 有标签：标签文件首字符为 0/1
  - 无标签：空文件（0 字节）或非 0/1 的内容
  - 划分策略：先从有标签中抽 test（默认 15%），再从剩余中按 val_ratio（默认 20%）划分 val，余下为 train（分层抽样）

## 训练（FixMatch + EMA）

- 基本命令（CUDA）
  - python training/train_fixmatch.py --use_class_weight
- DirectML（适用于无 NVIDIA CUDA 的 Windows 设备）
  - python training/train_fixmatch.py --device directml --use_class_weight
- 常用参数：
  - --epochs 150
  - --image_size 224
  - --labeled_bs 64 --unlabeled_bs 192（可根据显存增大）
  - --tau 0.95（伪标签阈值）
  - --lambda_u 1.0 --rampup_epochs 10（无监督损失权重与预热）
  - --ema_decay 0.999
  - --early_stop_patience 15（早停耐心值，默认15轮无改善则停止）
  - --early_stop_min_delta 0.001（最小改善阈值）

说明：

- 训练时会在 `runs/fixmatch_vit/` 下保存日志与最优权重 `best_ema.pth`
- 记录指标：sup/unsup loss、mask_rate（伪标签利用率）、val AUC/ACC/F1

## 评估

- 使用最优 EMA 权重：
  - python training/eval.py --checkpoint runs/fixmatch_vit/best_ema.pth
- DirectML 评估：
  - python training/eval.py --checkpoint runs/fixmatch_vit/best_ema.pth --device directml

## 重要实现细节

- 模型：timm 的 `vit_base_patch16_224`（768维，更好性能），使用标准 224×224 输入尺寸
- 数据增强：弱增强（弱 jitter + HFlip）、强增强（RandAugment + Cutout 适度），避免过度形变破坏纹理
- 类不均衡：`--use_class_weight` 启用基于频率的 class weights；也可在后续改为 focal loss
- 半监督损失：FixMatch（弱增强出伪标签，强增强做一致性），τ=0.95，λu 线性 ramp-up 到 1.0
- EMA：decay=0.999，用 EMA 模型做验证与保存

## 常见问题（FAQ）

- 训练很慢或显存不足？
  - 降低 batch：`--labeled_bs 32 --unlabeled_bs 96`
  - 关闭 AMP（默认 CUDA 开启，DirectML/CPU 关闭）或减少增强强度
- mask_rate 很低（伪标签利用率低）？
  - 将 `--tau 0.95` 降到 `0.9`，或增加训练轮数
  - 后续可考虑升级到 FreeMatch/SoftMatch 的自适应阈值/分布对齐
- 类分布严重不均衡？
  - 保持 `--use_class_weight`；必要时引入 WeightedRandomSampler 或 focal loss
- 如何改为 160 输入（节省显存）？
  - 训练脚本加 `--image_size 160`，但可能影响预训练权重的效果

## 后续工作

- 增加 FreeMatch/SoftMatch 的实现选项（参数开关）
- 在划分工具中支持“按原始大图分组”的泄漏控制
- 导出更详细的统计与可视化（类直方图、伪标签分布）
