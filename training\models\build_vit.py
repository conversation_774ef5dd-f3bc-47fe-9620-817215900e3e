import torch
import torch.nn as nn
import timm


def build_vit_model(num_classes: int = 2, image_size: int = 160, pretrained: bool = True, model_name: str = 'vit_base_patch16_224'):
    """
    Build a Vision Transformer model.

    Args:
        num_classes: Number of output classes
        image_size: Input image size
        pretrained: Whether to use pretrained weights
        model_name: Name of the ViT model to use. Options:
                   - 'vit_tiny_patch16_224' (embed_dim=192)
                   - 'vit_small_patch16_224' (embed_dim=384)
                   - 'vit_base_patch16_224' (embed_dim=768) [DEFAULT - better performance]
                   - 'vit_large_patch16_224' (embed_dim=1024)
    """
    print(f"Creating model: {model_name} with {num_classes} classes, pretrained={pretrained}")
    model = timm.create_model(model_name, pretrained=pretrained, num_classes=num_classes)
    print(f"Created model with embed_dim={model.embed_dim}, head shape={model.head.weight.shape}")
    # timm will handle pos_embed interpolation on first forward with different size
    return model


def infer_model_type_from_checkpoint(checkpoint_path):
    """
    Infer the model type from a checkpoint by examining the embedding dimensions.

    Returns:
        str: The model name that matches the checkpoint architecture
    """
    print(f"Loading checkpoint from: {checkpoint_path}")
    ckpt = torch.load(checkpoint_path, map_location='cpu')
    print(f"Checkpoint keys: {list(ckpt.keys())}")

    state_dict = ckpt.get('model_ema', ckpt)
    print(f"State dict has {len(state_dict)} parameters")

    # Check embedding dimension from various possible keys
    embed_dim = None

    # Try to get embed_dim from norm layer
    if 'norm.weight' in state_dict:
        embed_dim = state_dict['norm.weight'].shape[0]
        print(f"Found embed_dim={embed_dim} from norm.weight")
    elif 'blocks.0.norm1.weight' in state_dict:
        embed_dim = state_dict['blocks.0.norm1.weight'].shape[0]
        print(f"Found embed_dim={embed_dim} from blocks.0.norm1.weight")
    elif 'patch_embed.proj.weight' in state_dict:
        # For patch embedding: out_channels = embed_dim
        embed_dim = state_dict['patch_embed.proj.weight'].shape[0]
        print(f"Found embed_dim={embed_dim} from patch_embed.proj.weight")

    if embed_dim is None:
        print("Available keys in state_dict:")
        for key in sorted(state_dict.keys())[:20]:  # Show first 20 keys
            print(f"  {key}: {state_dict[key].shape}")
        raise ValueError("Could not determine embedding dimension from checkpoint")

    # Map embedding dimensions to model names
    embed_dim_to_model = {
        192: 'vit_tiny_patch16_224',
        384: 'vit_small_patch16_224',
        768: 'vit_base_patch16_224',
        1024: 'vit_large_patch16_224'
    }

    if embed_dim not in embed_dim_to_model:
        raise ValueError(f"Unsupported embedding dimension: {embed_dim}. Supported: {list(embed_dim_to_model.keys())}")

    model_name = embed_dim_to_model[embed_dim]
    print(f"Inferred model type: {model_name} (embed_dim={embed_dim})")
    return model_name

