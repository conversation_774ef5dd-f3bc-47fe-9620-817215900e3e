import argparse
from pathlib import Path

import torch
from torch.utils.data import DataLoader

from training.datasets.corn_patches import CornLabeledDataset
from training.datasets.transforms_fixmatch import build_transforms
from training.utils.metrics import binary_metrics_from_logits


def parse_args():
    ap = argparse.ArgumentParser()
    ap.add_argument('--splits_dir', type=Path, default=Path('training')/ 'data' / 'splits')
    ap.add_argument('--images_root', type=Path, default=Path('.'))
    ap.add_argument('--checkpoint', type=Path, required=True)
    ap.add_argument('--image_size', type=int, default=224)
    ap.add_argument('--device', type=str, default='cuda', choices=['cpu','cuda','directml'])
    return ap.parse_args()


def main():
    args = parse_args()
    if args.device == 'directml':
        import torch_directml
        device = torch_directml.device()
    elif args.device == 'cuda' and torch.cuda.is_available():
        device = torch.device('cuda')
    else:
        device = torch.device('cpu')

    _, _, eval_tfm = build_transforms(args.image_size)
    ds_test = CornLabeledDataset(args.splits_dir / 'labeled_test.csv', image_root=args.images_root, transform=eval_tfm)
    dl_test = DataLoader(ds_test, batch_size=256, shuffle=False, num_workers=8, pin_memory=True)

    from training.models.build_vit import build_vit_model, infer_model_type_from_checkpoint

    # Automatically detect the model type from checkpoint
    model_name = infer_model_type_from_checkpoint(args.checkpoint)
    print(f"Detected model type: {model_name}")

    ckpt = torch.load(args.checkpoint, map_location='cpu', weights_only=False)
    model = build_vit_model(num_classes=2, image_size=args.image_size, pretrained=False, model_name=model_name)
    model.load_state_dict(ckpt['model_ema'])
    model.to(device)
    model.eval()

    ys_all, logits_all = [], []
    with torch.no_grad():
        for x, y in dl_test:
            x = x.to(device)
            y = y.to(device)
            logits = model(x)
            ys_all.append(y)
            logits_all.append(logits)

    ys_all = torch.cat(ys_all, dim=0)
    logits_all = torch.cat(logits_all, dim=0)
    auc, acc, f1 = binary_metrics_from_logits(logits_all, ys_all)
    print(f"Test: AUC={auc:.4f}, ACC={acc:.4f}, F1={f1:.4f}")


if __name__ == '__main__':
    main()

