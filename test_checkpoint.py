#!/usr/bin/env python3

import sys
import torch
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from training.models.build_vit import build_vit_model, infer_model_type_from_checkpoint

def test_checkpoint_loading(checkpoint_path):
    """Test loading a checkpoint and see what happens"""
    
    print(f"Testing checkpoint: {checkpoint_path}")
    
    if not Path(checkpoint_path).exists():
        print(f"Checkpoint file does not exist: {checkpoint_path}")
        return
    
    try:
        # Try to infer model type from checkpoint
        model_name = infer_model_type_from_checkpoint(checkpoint_path)
        print(f"Detected model type: {model_name}")
        
        # Load checkpoint
        ckpt = torch.load(checkpoint_path, map_location='cpu')
        print(f"Checkpoint keys: {list(ckpt.keys())}")
        
        # Check state dict
        state_dict = ckpt.get('model_ema', ckpt)
        print(f"State dict keys (first 10): {list(state_dict.keys())[:10]}")
        
        # Check some key dimensions
        if 'norm.weight' in state_dict:
            print(f"norm.weight shape: {state_dict['norm.weight'].shape}")
        if 'head.weight' in state_dict:
            print(f"head.weight shape: {state_dict['head.weight'].shape}")
        if 'blocks.0.norm1.weight' in state_dict:
            print(f"blocks.0.norm1.weight shape: {state_dict['blocks.0.norm1.weight'].shape}")
            
        # Create model with detected type
        model = build_vit_model(num_classes=2, image_size=160, pretrained=False, model_name=model_name)
        print(f"Created model embed_dim: {model.embed_dim}")
        print(f"Created model norm shape: {model.norm.weight.shape}")
        print(f"Created model head shape: {model.head.weight.shape}")
        
        # Try to load state dict
        print("Attempting to load state dict...")
        model.load_state_dict(state_dict)
        print("✓ Successfully loaded checkpoint!")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Test with any checkpoint files that exist
    possible_checkpoints = [
        "runs/fixmatch_vit/best_ema.pth",
        "best_ema.pth",
        "checkpoint.pth"
    ]
    
    found_checkpoint = False
    for ckpt_path in possible_checkpoints:
        if Path(ckpt_path).exists():
            test_checkpoint_loading(ckpt_path)
            found_checkpoint = True
            break
    
    if not found_checkpoint:
        print("No checkpoint files found. Please provide a checkpoint path as argument.")
        print("Usage: python test_checkpoint.py <checkpoint_path>")
        
        if len(sys.argv) > 1:
            test_checkpoint_loading(sys.argv[1])
